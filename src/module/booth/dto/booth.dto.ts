import TransformDate from '@common/decorators/transform-date.decorator';
import { GoodsListDto } from '@module/goods/dto/goods.list.dto';
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { GoodsSellingMethodEnum } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Min,
} from 'class-validator';

export class BoothDto {
  @ApiProperty({
    description: '부스 uuid',
    example: '01968136-f8ae-7bc1-b7a1-dcdd972566f7',
  })
  @IsUUID('7')
  @IsNotEmpty()
  boothUuid!: string;

  @ApiProperty({
    description: '이벤트 uuid',
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID()
  @IsNotEmpty()
  eventUuid!: string;

  @ApiProperty({
    description: '부스 이름',
    example: '변경된 부스 이름',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    description: '행사 참여 일시들',
    isArray: true,
    example: [new Date().toISOString()],
  })
  @IsArray()
  @IsNotEmpty()
  @TransformDate()
  participatedDates!: Date[];

  @ApiProperty({
    description: '부스 위치 텍스트',
    isArray: true,
    example: ['L2', 'L3'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  locationTexts?: string[];

  @ApiProperty({
    description: '부스 소스 uuid',
    isArray: true,
    example: [
      '01968136-f8ae-7bc1-b7a1-dcdd972566f7',
      '01968136-f8ae-7bc1-b7a1-ac123f554362',
    ],
  })
  @IsArray()
  @IsUUID('7', { each: true })
  @IsNotEmpty()
  sourceUuids!: string[];

  @ApiProperty({
    description: '부스 배너 이미지 url',
    example: 'https://example.com/banner.jpg',
  })
  @IsString()
  @IsNotEmpty()
  bannerImageUrl!: string;

  @ApiProperty({
    isArray: true,
    description:
      'booth info list (index 0: booth description, index 1+: booth info image file list)',
    example: [
      'Hi, this is Sherry booth',
      'https://static.sherry.gg/booth/1.jpg',
      'https://static.sherry.gg/booth/2.jpg',
    ],
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  infoBlocks!: string[];

  @ApiProperty({
    description: '부스 생성 일시',
    example: new Date().toISOString(),
  })
  @IsDate()
  @IsNotEmpty()
  createdAt!: Date;

  @ApiProperty({
    description: 'goods list that booth has',
    type: 'array',
    example: [
      {
        goodsUuid: '123e4567-e89b-12d3-a456-************',
        amount: 10,
      },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  @Type(() => GoodsBoothPivotDto)
  goodsBoothPivot!: GoodsBoothPivotDto[];
}

export class GoodsBoothPivotDto {
  @ApiProperty({
    description: '굿즈 uuid',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID('7')
  goodsUuid!: string;

  @ApiProperty({
    description: 'selling methods that goods has on booth',
    type: 'array',
    enum: GoodsSellingMethodEnum,
    enumName: 'GoodsSellingMethodEnum',
    example: [
      GoodsSellingMethodEnum.PRE_ORDER,
      GoodsSellingMethodEnum.IN_PLACE,
    ],
    isArray: true,
    deprecated: true,
  })
  @IsArray()
  @IsNotEmpty()
  @IsEnum(GoodsSellingMethodEnum, { each: true })
  sellingMethods!: GoodsSellingMethodEnum[];

  @ApiProperty({
    description: 'amount of goods that booth has',
    type: Number,
    example: 10,
  })
  @IsInt()
  @Min(0)
  amount!: number;
}

export class GetGoodsForBoothManagementDto extends OmitType(GoodsListDto, [
  'boothUuids',
]) {}
